import {
  Operation,
  postExtensionRecord,
  selectExtensionsToCreate,
  selectOperation,
  updateTags,
  useFetchAnnexedDocuments,
  useFetchContractModelFrames
} from '@mynotary/frontend/legals/api';
import {
  useRegisteredLetterMandatesWatcher,
  getRegisteredLetterMandate
} from '@mynotary/frontend/registered-letters/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useSelector } from 'react-redux';
import { setPageTitle, useGlobalLoader } from '@mynotary/frontend/shared/util';
import {
  leaveOperation,
  selectCurrentOperationId,
  selectCurrentOperationIsLoading
} from '@mynotary/frontend/current-operation/api';
import { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { getOperationInvitations } from '@mynotary/frontend/operation-invitations/api';
import { getOperationDefaultRecords } from '@mynotary/frontend/operation-default-records/api';
import { getOrders, resetOrder } from '@mynotary/frontend/orders/api';
import { useLegalOperationsWatcher } from './use-legal-operations-fetch.watcher';

export const useFetchOperation = (operationId: number): Operation => {
  useRegisteredLetterMandatesWatcher(operationId);
  useLegalOperationsWatcher(operationId);
  useFetchContractModelFrames(operationId);
  const dispatch = useAsyncDispatch();
  const operation = useSelector(selectOperation(operationId));

  const extensions = useSelector(selectExtensionsToCreate(operationId));
  const [setIsLoading] = useGlobalLoader(false);
  const loadedOperationId = useSelector(selectCurrentOperationId);
  const isCurrentOpeLoading = useSelector(selectCurrentOperationIsLoading);

  useEffect(() => {
    if (!isEmpty(extensions)) {
      dispatch(postExtensionRecord(operationId, extensions));
    }
  }, [dispatch, extensions, operationId]);

  const [hasUpdateTagOnMount, setHasUpdateTagOnMount] = useState(false);

  /**
   * This piece of code is used to call updateTags only once when the component is mounted and the operation is loaded.
   * It prove useful for interconnection scenarios, such as AC3, since there are no backend methods to update tags and compute labels.
   */
  useEffect(() => {
    if (operation && !hasUpdateTagOnMount && !isCurrentOpeLoading) {
      setHasUpdateTagOnMount(true);
      dispatch(updateTags(operationId));
    }
  }, [dispatch, operation, operationId, hasUpdateTagOnMount, isCurrentOpeLoading]);

  useEffect(() => {
    setIsLoading(isCurrentOpeLoading);
  }, [isCurrentOpeLoading, setIsLoading]);

  useEffect(() => {
    return () => {
      dispatch(leaveOperation());
    };
  }, [dispatch]);

  useEffect(() => {
    if (operationId) {
      dispatch(getOrders({ operationId }));
    }

    return () => {
      dispatch(resetOrder());
    };
  }, [dispatch, operationId]);

  useEffect(() => {
    setPageTitle(operation?.label ?? 'Dossier');
  }, [operation?.label]);

  useEffect(() => {
    if (loadedOperationId) {
      dispatch(getOperationInvitations(loadedOperationId));
    }
  }, [dispatch, loadedOperationId]);

  useEffect(() => {
    if (loadedOperationId) {
      dispatch(getOperationDefaultRecords(loadedOperationId));
    }
  }, [dispatch, loadedOperationId]);

  useEffect(() => {
    if (loadedOperationId) {
      dispatch(getRegisteredLetterMandate(loadedOperationId));
    }
  }, [dispatch, loadedOperationId]);

  useFetchAnnexedDocuments(operationId);

  return operation;
};
