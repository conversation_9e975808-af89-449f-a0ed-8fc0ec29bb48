import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { forEach } from 'lodash';
import { RegisterEntry } from '@mynotary/frontend/registers/core';

type Registers = {
  [type: string]: {
    [id: number]: RegisterEntry;
  };
};

type RegistersNew = {
  entries: RegisterEntry[];
};

const initialState: Registers = {};

export const registersSlice = createSlice({
  initialState,
  name: 'registers',
  reducers: {
    addRegisterEntries: (state, action: PayloadAction<RegisterEntry[]>) => {
      const entries = action.payload;

      forEach(entries, (entry) => {
        if (!state[entry.type]) {
          state[entry.type] = {};
        }

        state[entry.type][entry.id] = entry;
      });
    }
  }
});

export interface RegisterState {
  [registersSlice.name]: Registers;
}

export const selectRegistersFeature = (state: RegisterState) => state[registersSlice.name];
