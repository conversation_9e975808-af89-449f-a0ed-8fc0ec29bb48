import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import { selectCurrentTransactionRegister } from '@mynotary/frontend/registers/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestion } from './register-question';
import { RegisterQuestionWithoutAction } from './register-question-without-action';
import { some } from 'lodash';
import { selectContract } from '@mynotary/frontend/legals/api';
import { selectCurrentContractId } from '@mynotary/frontend/current-operation/api';
import { selectRegisterEntry } from '@mynotary/frontend/registers/store';

interface RegisterTransactionQuestionProps extends MnProps {
  answer: Answer;
  className?: string;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterTransactionQuestion = (props: RegisterTransactionQuestionProps): ReactElement => {
  const role = useSelector(selectConnectedUserRole);
  const contractId = useSelector(selectCurrentContractId);
  const contract = useSelector(selectContract(contractId));

  const transactionRegister = useSelector(selectCurrentTransactionRegister);
  const registerEntries = useSelector(selectRegisterEntry);
  if (registerEntries.length > 0) {
    props.disabled = true;
  }
  const { isActive: hasTransactionFeature } = useFeatureState(FeatureType.TRANSACTION_REGISTER_ACCESS);
  const isAllowedContract = some(
    props.question.register.contracts,
    (contractType) => contractType === contract?.legalContractTemplateId
  );
  const isRegisterInitialized = transactionRegister?.config != null;

  const hasRegisterEntryCreatePermission = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    EntityType.TRANSACTION_REGISTER
  );

  const hasRegisterEntryManuallyCreatePermission =
    !isRegisterInitialized ||
    hasPermission(PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY, role, EntityType.TRANSACTION_REGISTER);

  if (
    !props.disabled &&
    hasTransactionFeature &&
    hasRegisterEntryCreatePermission &&
    isAllowedContract &&
    isRegisterInitialized
  ) {
    return (
      <RegisterQuestion
        {...props}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
      />
    );
  } else {
    return (
      <RegisterQuestionWithoutAction
        {...props}
        hasRegisterEntryCreatePermission={hasRegisterEntryCreatePermission}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        isAllowedContract={isAllowedContract}
        isRegisterInitialized={isRegisterInitialized}
      />
    );
  }
};
